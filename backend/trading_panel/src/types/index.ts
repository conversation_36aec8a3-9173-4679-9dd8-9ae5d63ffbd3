// Trading Panel Types
export interface TradingPanelConfig {
  id: string;
  name: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Trade Data Types (migrated from frontend)
export interface Trade {
  id?: string;
  timestamp?: number;
  date?: number; // Mobula API might use 'date' field
  type: 'buy' | 'sell';
  token_price?: number;
  token_amount?: number;
  token_amount_vs?: number; // SOL amount in trades
  token_amount_usd?: number;
  hash?: string;
  transaction_hash?: string;
  tx_hash?: string;
  sender?: string;
  pairData?: {
    price?: number;
    token0?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    token1?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    liquidity?: number;
  };
  // Additional fields from Mobula API
  marketCap?: number;
  amount?: number;
  usd_amount?: number;
  value_usd?: number;
  price?: number;
  symbol?: string;
  blockchain?: string;
  chain?: string;
}

export interface FormattedTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: string;
  usdAmount: string;
  mc: string;
  price: string;
  trader: string;
  age: string;
  txHash: string;
  marketCap: number;
  tokenAmount: number; // SOL amount (token_amount_vs) - used for side panel
  tokenAmountUsd: number;
  actualTokenAmount: number; // Actual token quantity (token_amount) - used for bottom table
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: 'trade' | 'error' | 'connected' | 'disconnected';
  data?: any;
  error?: string;
}

export interface SubscriptionRequest {
  action: 'subscribe' | 'unsubscribe';
  poolAddress: string;
}
