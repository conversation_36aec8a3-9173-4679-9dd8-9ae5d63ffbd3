import { Server as HttpServer } from 'http';
import WebSocket, { WebSocketServer } from 'ws';
import { MobulaWebSocketService } from './mobulaWebSocketService';
import { FormattedTrade, WebSocketMessage, SubscriptionRequest } from '../types';

export class WebSocketServerService {
  private wss: WebSocketServer;
  private mobulaService: MobulaWebSocketService;
  private clients: Set<WebSocket> = new Set();
  private currentPoolAddress: string | null = null;

  constructor(server: HttpServer) {
    this.wss = new WebSocketServer({ server });
    this.mobulaService = new MobulaWebSocketService();
    
    this.setupMobulaCallbacks();
    this.setupWebSocketServer();
    
    console.log('🔧 WebSocket Server initialized');
  }

  private setupMobulaCallbacks(): void {
    // Handle new trades from Mobula
    this.mobulaService.onTrade((trade: FormattedTrade) => {
      console.log('📊 Broadcasting trade to clients:', trade.id);
      this.broadcastToClients({
        type: 'trade',
        data: trade
      });
    });

    // Handle Mobula connection errors
    this.mobulaService.onError((error: string) => {
      console.error('❌ Mobula error:', error);
      this.broadcastToClients({
        type: 'error',
        error: error
      });
    });

    // Handle Mobula connection status
    this.mobulaService.onConnection((connected: boolean) => {
      console.log(`🔌 Mobula connection status: ${connected ? 'Connected' : 'Disconnected'}`);
      this.broadcastToClients({
        type: connected ? 'connected' : 'disconnected',
        data: { connected, poolAddress: this.currentPoolAddress }
      });
    });
  }

  private setupWebSocketServer(): void {
    this.wss.on('connection', (ws: WebSocket) => {
      console.log('👤 New client connected');
      this.clients.add(ws);

      // Send current connection status
      const status = this.mobulaService.getConnectionStatus();
      ws.send(JSON.stringify({
        type: status.connected ? 'connected' : 'disconnected',
        data: status
      }));

      // Handle client messages
      ws.on('message', (data: WebSocket.Data) => {
        try {
          const message: SubscriptionRequest = JSON.parse(data.toString());
          this.handleClientMessage(ws, message);
        } catch (error) {
          console.error('❌ Error parsing client message:', error);
          ws.send(JSON.stringify({
            type: 'error',
            error: 'Invalid message format'
          }));
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        console.log('👤 Client disconnected');
        this.clients.delete(ws);
        
        // If no clients left, disconnect from Mobula
        if (this.clients.size === 0) {
          console.log('📡 No clients left, disconnecting from Mobula');
          this.mobulaService.disconnect();
          this.currentPoolAddress = null;
        }
      });

      // Handle client errors
      ws.on('error', (error: Error) => {
        console.error('❌ Client WebSocket error:', error);
        this.clients.delete(ws);
      });
    });
  }

  private handleClientMessage(ws: WebSocket, message: SubscriptionRequest): void {
    console.log('📨 Received client message:', message);

    switch (message.action) {
      case 'subscribe':
        this.handleSubscribe(ws, message.poolAddress);
        break;
      
      case 'unsubscribe':
        this.handleUnsubscribe(ws);
        break;
      
      default:
        ws.send(JSON.stringify({
          type: 'error',
          error: 'Unknown action'
        }));
    }
  }

  private async handleSubscribe(ws: WebSocket, poolAddress: string): Promise<void> {
    try {
      console.log(`📡 Client subscribing to pool: ${poolAddress}`);
      
      // If this is a new pool or no current connection
      if (this.currentPoolAddress !== poolAddress) {
        this.currentPoolAddress = poolAddress;
        await this.mobulaService.connect(poolAddress);
      }

      // Confirm subscription to client
      ws.send(JSON.stringify({
        type: 'connected',
        data: { 
          connected: true, 
          poolAddress: poolAddress,
          message: 'Successfully subscribed to trade feed'
        }
      }));

    } catch (error: any) {
      console.error('❌ Error subscribing to pool:', error);
      ws.send(JSON.stringify({
        type: 'error',
        error: `Failed to subscribe: ${error.message}`
      }));
    }
  }

  private handleUnsubscribe(ws: WebSocket): void {
    console.log('📡 Client unsubscribing');
    
    ws.send(JSON.stringify({
      type: 'disconnected',
      data: { 
        connected: false, 
        message: 'Unsubscribed from trade feed'
      }
    }));

    // If this was the last client, disconnect from Mobula
    if (this.clients.size <= 1) {
      console.log('📡 Last client unsubscribing, disconnecting from Mobula');
      this.mobulaService.disconnect();
      this.currentPoolAddress = null;
    }
  }

  private broadcastToClients(message: WebSocketMessage): void {
    const messageStr = JSON.stringify(message);
    
    this.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        try {
          client.send(messageStr);
        } catch (error) {
          console.error('❌ Error sending message to client:', error);
          this.clients.delete(client);
        }
      } else {
        // Remove closed clients
        this.clients.delete(client);
      }
    });

    console.log(`📡 Broadcasted ${message.type} to ${this.clients.size} clients`);
  }

  // Get server status
  public getStatus(): { 
    clientCount: number; 
    mobulaConnected: boolean; 
    currentPool: string | null;
  } {
    const mobulaStatus = this.mobulaService.getConnectionStatus();
    return {
      clientCount: this.clients.size,
      mobulaConnected: mobulaStatus.connected,
      currentPool: mobulaStatus.poolAddress
    };
  }

  // Shutdown the server
  public shutdown(): void {
    console.log('🔌 Shutting down WebSocket server...');
    
    // Disconnect from Mobula
    this.mobulaService.disconnect();
    
    // Close all client connections
    this.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.close(1000, 'Server shutdown');
      }
    });
    
    // Close the WebSocket server
    this.wss.close(() => {
      console.log('✅ WebSocket server shut down');
    });
  }
}
