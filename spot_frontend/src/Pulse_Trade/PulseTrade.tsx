import Navbar from "@/Home/Navbar/Navbar"
import { useState, useEffect } from "react";
import { useParams, useLocation } from "react-router-dom";
import { ChevronLeft, ChevronRight } from "lucide-react";
import PriceChartWidget from "./PriceChartWidget";
import Pulse_Token from "./Pulse_Token";
import Trades from "./Trades";
import TradingPanel from "./TradingPanel";
import Tables from "./Tables/Tables";
import Footer from "@/Pulse_Trade/Footer/Footer";
import { TradingStats } from "./Trading_Stats";
import PulseWishList from "./PulseWishlist";
import usePulseData from "@/hooks/usePulseData";
import { homeAPI } from "@/utils/api";
import { useBackendTradeData } from "@/hooks/useBackendTradeData";

export default function Pulse_Trade() {
   


    const [isSearchOpen, setIsSearchOpen] = useState(false);
    const [isTradesTableVisible, setIsTradesTableVisible] = useState(false);
    const [isLoadingToken, setIsLoadingToken] = useState(false);
    const [activeTokenData, setActiveTokenData] = useState<any>(null);

    // Get shared trade data for both side panel and bottom table
    const tradeData = useBackendTradeData(null);

    const toggleTradesTable = () => {
        setIsTradesTableVisible(!isTradesTableVisible);
    };

    const { address } = useParams();
    const location = useLocation();
    const { fetchAndAddTokenByAddress } = usePulseData();
    // useEffect(() => {
    //     const fetchHoldersData = async () => {
    //       if (!address) return;
    
    //       try {
    //         const response = await homeAPI.fetchMarketInfo('holders', {
    //           address,
    //           network: 'solana',
              
    //         });
    
    //         const data = response.data?.data;
    //         console.log('Holders data received:', data);
       
    //       } catch (error) {
    //         console.error('Failed to fetch holders data:', error);
    //       }
    //     };
    
    //     fetchHoldersData();
    //   }, [address]);
    // Automatically fetch and add token data when navigating to trade page with address
    useEffect(() => {
        const loadTokenData = async () => {
            // console.log("🎯 PULSE TRADE COMPONENT - Starting token data loading process");
            // console.log("🎯 URL Address parameter:", address);
            // console.log("🎯 Current location:", window.location.href);
            // console.log("🎯 useEffect triggered - this should fire when address parameter changes");
            // console.log("🎯 useEffect dependency values:", { address, fetchAndAddTokenByAddress: !!fetchAndAddTokenByAddress });

            // Check for navigation data from search
            const lastNavigation = localStorage.getItem("lastSearchNavigation");
            if (lastNavigation) {
                console.log("🎯 Found search navigation data:", JSON.parse(lastNavigation));
            }

            if (!address) {
                console.log('❌ No token address provided in URL');
                return;
            }

            setIsLoadingToken(true);
            console.log(`🎯 Loading token data for address: ${address}`);
            console.log(`🎯 Address type: ${typeof address}, length: ${address.length}`);

            try {
                // Check current localStorage state
                const existingPulseTokens = JSON.parse(localStorage.getItem('pulseTokens') || '[]');
                const existingActiveToken = localStorage.getItem("activePulseToken");
                const existingSearchToken = localStorage.getItem('lastSearchNavigation');

                console.log("🎯 CURRENT LOCALSTORAGE STATE:");
                console.log("  - pulseTokens count:", existingPulseTokens.length);
                console.log("  - activePulseToken exists:", !!existingActiveToken);
                console.log("  - activeToken (from search) exists:", !!existingSearchToken);

                // Check if token is already loaded as activePulseToken
                if (existingActiveToken) {
                    const parsedToken = JSON.parse(existingActiveToken);
                    console.log("🎯 Existing active token:", parsedToken);
                    if (parsedToken.address === address || parsedToken.id === address) {
                        console.log('✅ Token already loaded as active token');
                        setActiveTokenData(parsedToken);
                        setIsLoadingToken(false);
                        return;
                    }
                }

                // Check if we have search token data that matches
                if (existingSearchToken) {
                    const searchToken = JSON.parse(existingSearchToken);
                    const searchTokenAddress = searchToken.id || searchToken.address || searchToken.contract;

                    console.log("🎯 COMPARING SEARCH TOKEN WITH URL:");
                    console.log("  - URL address:", address);
                    console.log("  - Search token address:", searchTokenAddress);
                    console.log("  - Addresses match:", searchTokenAddress === address);

                    if (searchTokenAddress === address) {
                        console.log("🎯 Using search token data for initial display");

                        // Transform search token to pulse token format
                        const transformedToken = {
                            id: searchToken.id || searchToken.address,
                            address: searchToken.id || searchToken.address,
                            name: searchToken.name || 'Unknown Token',
                            symbol: searchToken.symbol || 'UNKNOWN',
                            network: searchToken.network || 'solana',
                            current_price: searchToken.current_price || 0,
                            price_change_percentage_24h: searchToken.price_change_percentage_24h || 0,
                            market_cap: searchToken.market_cap || 0,
                            total_volume: searchToken.total_volume || 0,
                            imageUrl: searchToken.image || '',
                            // Add default values for missing fields
                            bonding_percent: searchToken.bonding_percent || searchToken.bonding ||0,
                            volume: searchToken.total_volume || 0,
                            price: searchToken.current_price || 0,
                            liquidity: searchToken.liquidity || 0,
                            supply: 0,
                            exchange_name: searchToken.exchange_name || 'Unknown',
                            exchange_logo: searchToken.exchange_logo || '',
                            pool_address: searchToken.pool_address || '', // Preserve pool address from search!
                            pair_type: searchToken.pair_type || '',
                            price_change_24h: searchToken.price_change_percentage_24h || 0,
                            // Keep original search data for reference
                            _originalSearchData: searchToken
                        };

                        console.log("🎯 TRANSFORMED TOKEN WITH POOL DATA:", {
                            symbol: transformedToken.symbol,
                            tokenAddress: transformedToken.address,
                            poolAddress: transformedToken.pool_address,
                            pairType: transformedToken.pair_type,
                            exchange: transformedToken.exchange_name,
                            hasPoolAddress: !!transformedToken.pool_address
                        });

                        setActiveTokenData(transformedToken);
                        // localStorage.setItem('activePulseToken', JSON.stringify(transformedToken));
                        console.log("🎯 Transformed and stored search token:", transformedToken);
                    }
                }

                // Fetch token data and add to wishlist
                console.log(`PulseTrade: About to fetch token data for ${address}`);
                const success = await fetchAndAddTokenByAddress(address, 'solana');
                if (success) {
                    console.log(`PulseTrade: Successfully loaded token data for ${address}`);
                    // Check localStorage state after successful fetch
                    const pulseTokens = JSON.parse(localStorage.getItem('pulseTokens') || '[]');
                    const activePulseToken = localStorage.getItem('activePulseToken');
                    console.log(`PulseTrade: After fetch - pulseTokens count: ${pulseTokens.length}, activePulseToken set: ${!!activePulseToken}`);

                    // Load the active token data for chart enhancement
                    if (activePulseToken) {
                        const tokenData = JSON.parse(activePulseToken);
                        setActiveTokenData(tokenData);
                        console.log('📊 Loaded token data for chart:', {
                            symbol: tokenData.symbol,
                            pool_address: tokenData.pool_address,
                            address: tokenData.address || tokenData.id
                        });
                    }
                } else {
                    console.error(`❌ PulseTrade: Failed to load token data for ${address}`);
                    console.error("❌ This might be due to API issues or invalid token address");

                    // If we have search token data, use it as fallback
                    if (existingSearchToken) {
                        const searchToken = JSON.parse(existingSearchToken);
                        const searchTokenAddress = searchToken.id || searchToken.address || searchToken.contract;

                        if (searchTokenAddress === address) {
                            console.log("🎯 API failed - Using search token as fallback display data");

                            // Use the same transformation as above
                            const transformedToken = {
                                id: searchToken.id || searchToken.address,
                                address: searchToken.id || searchToken.address,
                                name: searchToken.name || 'Unknown Token',
                                symbol: searchToken.symbol || 'UNKNOWN',
                                network: searchToken.network || 'solana',
                                current_price: searchToken.current_price || 0,
                                price_change_percentage_24h: searchToken.price_change_percentage_24h || 0,
                                market_cap: searchToken.market_cap || 0,
                                total_volume: searchToken.total_volume || 0,
                                imageUrl: searchToken.image || '',
                                // Add default values for missing fields
                                bonding_percent: searchToken.bonding_percent || searchToken.bonding ||0,    
                                volume: searchToken.total_volume || 0,
                                price: searchToken.current_price || 0,
                                liquidity: searchToken.liquidity || 0,
                                supply: 0,
                                exchange_name: searchToken.exchange_name || 'Unknown',
                                exchange_logo: searchToken.exchange_logo || '',
                                pool_address: searchToken.pool_address || '', // Preserve pool address from search!
                                pair_type: searchToken.pair_type || '',
                                price_change_24h: searchToken.price_change_percentage_24h || 0,
                                // Mark as fallback data
                                _isSearchFallback: true,
                                _originalSearchData: searchToken
                            };

                            console.log("🎯 FALLBACK TOKEN WITH POOL DATA:", {
                                symbol: transformedToken.symbol,
                                tokenAddress: transformedToken.address,
                                poolAddress: transformedToken.pool_address,
                                pairType: transformedToken.pair_type,
                                exchange: transformedToken.exchange_name,
                                hasPoolAddress: !!transformedToken.pool_address,
                                isFallback: true
                            });

                            setActiveTokenData(transformedToken);
                            // localStorage.setItem('activePulseToken', JSON.stringify(transformedToken));
                            console.log("🎯 Set fallback token data:", transformedToken);
                        }
                    }
                }
            } catch (error) {
                console.error('❌ Error loading token data:', error);
            } finally {
                setIsLoadingToken(false);
                console.log("🎯 Token loading process completed");

                // Final state check
                const finalActiveToken = localStorage.getItem('activePulseToken');
                console.log("🎯 FINAL STATE CHECK:");
                console.log("  - activePulseToken exists:", !!finalActiveToken);
                if (finalActiveToken) {
                    const finalToken = JSON.parse(finalActiveToken);
                    console.log("  - Final active token:", {
                        symbol: finalToken.symbol,
                        address: finalToken.address || finalToken.id,
                        hasPoolAddress: !!finalToken.pool_address
                    });
                }
            }
        };

        loadTokenData();
    }, [address]);

    // Additional useEffect to watch for location changes (React Router navigation)
    useEffect(() => {
        console.log('🔄 PulseTrade: Location changed detected');
        console.log('🔄 PulseTrade: New location:', location.pathname);
        console.log('🔄 PulseTrade: Address from params:', address);

        // Force update activeTokenData when location changes
        const activePulseToken = localStorage.getItem('activePulseToken');
        if (activePulseToken) {
            try {
                const tokenData = JSON.parse(activePulseToken);
                const tokenAddress = tokenData.address || tokenData.id;

                console.log('🔄 PulseTrade: Location change - updating activeTokenData');
                console.log('🔄 PulseTrade: Token from localStorage:', {
                    symbol: tokenData.symbol,
                    address: tokenAddress,
                    matchesUrlParam: tokenAddress === address
                });

                setActiveTokenData(tokenData);
            } catch (error) {
                console.error('🔄 PulseTrade: Error parsing activePulseToken on location change:', error);
            }
        }
    }, [location.pathname, address]);

    // Load existing active token data on component mount and listen for changes
    useEffect(() => {
        const loadActiveTokenData = () => {
            const activePulseToken = localStorage.getItem('activePulseToken');
            if (activePulseToken) {
                const tokenData = JSON.parse(activePulseToken);
                setActiveTokenData(tokenData);
                console.log('📊 Loaded existing token data for chart:', {
                    symbol: tokenData.symbol,
                    pool_address: tokenData.pool_address,
                    address: tokenData.address || tokenData.id
                });
            }
        };

        // Load on mount
        loadActiveTokenData();

        // Listen for pulse data changes
        const handlePulseDataChange = (event: any) => {
            console.log('📊 PulseTrade: Received pulseDataChanged event:', event.detail);

            if (event.detail?.activePulseToken) {
                const newTokenData = event.detail.activePulseToken;
                const newAddress = newTokenData.address || newTokenData.id;

                console.log('📊 PulseTrade: Processing token data update:', {
                    currentAddress: address,
                    newAddress: newAddress,
                    symbol: newTokenData.symbol,
                    source: event.detail.source || 'unknown',
                    forceUpdate: event.detail.forceUpdate
                });

                // Always update the active token data
                setActiveTokenData(newTokenData);
                console.log('📊 PulseTrade: Updated activeTokenData state');

                // If this is a different token and we're on pulse-trade page, trigger additional updates
                if (newAddress && newAddress !== address && window.location.pathname.startsWith('/pulse-trade/')) {
                    console.log('📊 PulseTrade: Different token detected, triggering additional updates');

                    // Force a re-render by updating loading state briefly
                    setIsLoadingToken(true);
                    setTimeout(() => setIsLoadingToken(false), 100);
                }
            }
        };

        // Listen for forced token changes when already on pulse-trade page
        const handlePulseTradeTokenChange = async (event: any) => {
            console.log('🔄 PulseTrade: Received pulseTradeTokenChange event:', event.detail);
            const { tokenAddress, immediate, delayed } = event.detail;

            console.log('🔄 PulseTrade: Event details:', {
                tokenAddress,
                currentAddress: address,
                immediate,
                delayed,
                addressMatch: tokenAddress === address
            });

            if (tokenAddress) {
                console.log(`🔄 PulseTrade: Processing token change to ${tokenAddress}`);

                // Always update active token data from localStorage
                const activePulseToken = localStorage.getItem('activePulseToken');
                if (activePulseToken) {
                    try {
                        const tokenData = JSON.parse(activePulseToken);
                        setActiveTokenData(tokenData);
                        console.log('🔄 PulseTrade: Updated activeTokenData from localStorage');
                    } catch (error) {
                        console.error('🔄 PulseTrade: Error parsing activePulseToken:', error);
                    }
                }

                // If this is a different address, force reload
                // if (tokenAddress !== address) {
                //     console.log(`🔄 PulseTrade: Address mismatch, force reloading token data`);
                //     setIsLoadingToken(true);

                //     try {
                //         const success = await fetchAndAddTokenByAddress(tokenAddress, 'solana');
                //         if (success) {
                //             console.log(`🔄 PulseTrade: Successfully force-loaded token data for ${tokenAddress}`);
                //         } else {
                //             console.warn(`🔄 PulseTrade: Failed to load token data for ${tokenAddress}`);
                //         }
                //     } catch (error) {
                //         console.error('🔄 PulseTrade: Error during force token reload:', error);
                //     } finally {
                //         setIsLoadingToken(false);
                //     }
                // } else {
                //     console.log('🔄 PulseTrade: Same address, just updating display data');
                // }
            }
        };

        window.addEventListener('pulseDataChanged', handlePulseDataChange);
        window.addEventListener('pulseTradeTokenChange', handlePulseTradeTokenChange);

        return () => {
            window.removeEventListener('pulseDataChanged', handlePulseDataChange);
            window.removeEventListener('pulseTradeTokenChange', handlePulseTradeTokenChange);
        };
    }, [address]);

    // Sample trades data

    return (
        <div
            key={`pulse-trade-${address}`} // Force re-render when address changes
            className={`relative bg-[#141416] min-h-[150vh] ${isSearchOpen ? "overflow-hidden" : ""}`}
        >
            {isSearchOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-lg transition-opacity duration-300 z-40"
                    onClick={() => setIsSearchOpen(false)}
                />
            )}
    
            <Navbar />
            <PulseWishList />
    
            <div className="flex w-screen overflow-x-hidden overflow-y-auto min-h-[110vh]">

                {/* Left Section */}
                <div className={`flex flex-col min-h-[110vh] transition-all duration-300 ${isTradesTableVisible ? 'w-3/4' : 'w-3/4'}`}>
                    
                    {/* Token Stats */}
                    <div className="h-[5rem] border-b border-gray-700 flex-shrink-0">
                        <Pulse_Token  />
                    </div>
    
                    {/* Trading Chart + Trades */}
                    <div className="h-[60vh] flex flex-shrink-0">
                        <div className={`relative border-b border-gray-700 p-4 transition-all duration-300 ${isTradesTableVisible ? 'w-3/4' : 'w-full'}`}>
                            <div className="h-full rounded-lg text-white">
                                {address ? (
                                    <>
                                        {/* Enhanced logging for chart address resolution */}
                                        {console.log('📊 Chart Address Resolution:', {
                                            tokenAddress: address,
                                            pairAddress: activeTokenData?.pool_address,
                                            willUsePairAddress: !!activeTokenData?.pool_address,
                                            improvement: activeTokenData?.pool_address
                                                ? '✅ Using pair address for better chart accuracy'
                                                : '⚠️ Using token address (pair data not yet loaded)',
                                            tokenSymbol: activeTokenData?.symbol || 'Loading...'
                                        })}
                                        <PriceChartWidget
                                            tokenAddress={address}
                                            pairAddress={activeTokenData?.pool_address}
                                            chainId="solana"
                                            theme="dark"
                                            defaultInterval="1"
                                            showHoldersChart={true}
                                            hideLeftToolbar={false}
                                            hideTopToolbar={false}
                                            hideBottomToolbar={false}
                                        />
                                    </>
                                ) : (
                                    <div className="h-full flex items-center justify-center text-gray-400">
                                        No token address provided
                                    </div>
                                )}
                            </div>

                            {!isTradesTableVisible && (
                                <button
                                    onClick={toggleTradesTable}
                                    className="absolute top-1/2 -translate-y-1/2 -right-4 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg shadow-lg transition-all duration-200 z-10 border border-gray-600"
                                    title="Show Trades Table"
                                >
                                    <ChevronLeft size={20} />
                                </button>
                            )}
                        </div>

                        {isTradesTableVisible && (
                            <div className="w-1/4 border-b border-gray-700 border-l border-gray-700 relative">
                                <button
                                    onClick={toggleTradesTable}
                                    className="absolute top-1/2 -translate-y-1/2 -left-4 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg shadow-lg transition-all duration-200 z-10 border border-gray-600"
                                    title="Hide Trades Table"
                                >
                                    <ChevronRight size={20} />
                                </button>

                                <div className="h-full rounded-lg overflow-y-auto">
                                    <Trades tradeData={tradeData} />
                                </div>
                            </div>
                        )}
                    </div>
    
                    {/* Tables Section - Always visible, but Trades tab conditionally hidden */}
                    <div
                        className="relative -mt-6 pt-8 border-t border-gray-700 flex-1 min-h-0 z-20"
                        style={{
                            backgroundColor: '#141416',
                            boxShadow: '0 -10px 20px rgba(20, 20, 22, 0.9)'
                        }}
                    >
                        <Tables isTradesTableVisible={isTradesTableVisible} tradeData={tradeData} />
                    </div>
                </div>
    
                {/* Right Panel */}
                <div className="flex flex-col min-h-[150vh] w-1/4 border-l border-gray-700">
                    <div className="h-[5rem] border-b border-gray-700 flex-shrink-0">
                        <TradingStats />
                    </div>
                    <div className="h-full">
                        <TradingPanel />
                    </div>
                </div>
            </div>
    
            <Footer />
        </div>
    );
    
}