import React, { useState, useEffect } from 'react';
import { ChevronUp, ChevronDown, DollarSign, RefreshCcw, ArrowLeftRight, Filter, User } from 'lucide-react';

interface TradesProps {
  tradeData?: {
    trades: any[];
    isLoading: boolean;
    isConnected: boolean;
    error: string | null;
    lastUpdate: number | null;
    latestRawTrade: any;
  };
}

const Trades: React.FC<TradesProps> = ({ tradeData }) => {
  const [activeTab, setActiveTab] = useState('TRADES');
  const [sortAsc, setSortAsc] = useState(true); // true = newest first (desc), false = oldest first (asc)
  const [showPrice, setShowPrice] = useState(false);
  const [showUSD, setShowUSD] = useState(false);
  const [currentTime, setCurrentTime] = useState(Date.now());

  // Use trade data from props (shared with bottom table)
  const { trades = [], isLoading = false, isConnected = false, error = null, lastUpdate = null } = tradeData || {};

  // Real-time age updates (similar to Pulse table implementation)
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, []);

  // Format age with real-time updates and robust error handling
  const formatRealTimeAge = (timestamp: number): string => {
    // Handle invalid timestamps
    if (!timestamp || isNaN(timestamp) || timestamp <= 0) {
      console.warn('⚠️ Invalid timestamp in formatRealTimeAge:', timestamp);
      return 'unknown';
    }

    const diffSeconds = Math.floor((currentTime - timestamp) / 1000);

    // Handle negative or invalid time differences
    if (isNaN(diffSeconds) || diffSeconds < 0) {
      console.warn('⚠️ Invalid time difference:', { currentTime, timestamp, diffSeconds });
      return 'unknown';
    }

    if (diffSeconds < 1) {
      return 'just now';
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffSeconds / 86400)}d ago`;
    }
  };

  // Handle trader hash click to open Solscan
  const handleTraderClick = (txHash: string) => {
    if (txHash && txHash !== 'N/A') {
      const solscanUrl = `https://solscan.io/tx/${txHash}`;
      window.open(solscanUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // Debug trade data
  useEffect(() => {
    console.log('📊 Trades: Hook data update:', {
      tradesCount: trades.length,
      isLoading,
      isConnected,
      error,
      lastUpdate: lastUpdate ? new Date(lastUpdate).toLocaleTimeString() : null
    });
  }, [trades, isLoading, isConnected, error, lastUpdate]);

  const getCurrentData = () => {
    let data;

    if (activeTab === 'DEV') {
      // Filter trades by dev wallets - this would need real dev wallet identification
      data = trades.filter(trade => trade.trader === 'Dev');
    } else if (activeTab === 'YOU') {
      // Filter trades by user's wallet - this would need user wallet identification
      data = trades.filter(trade => trade.trader === 'You');
    } else {
      // Use real trade data for TRADES tab
      data = trades;
    }

    // Default to descending order (newest first) - flip the sort logic
    return [...data].sort((a, b) => {
      return sortAsc ? b.timestamp - a.timestamp : a.timestamp - b.timestamp;
    });
  };

  return (
    <div className="text-white h-full font-mono text-sm flex flex-col">
      {/* Header Tabs */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        {/* Connection Status Indicator */}
        {activeTab === 'TRADES' && (
          <div className="flex items-center space-x-2 text-xs">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-gray-400">
              {isLoading ? 'Loading...' : isConnected ? `Live • ${trades.length} trades` : 'Disconnected'}
            </span>
            {error && <span className="text-red-400">Error: {error}</span>}
          </div>
        )}

        {/* Left side: TRADES */}
        <div>
          <button
            onClick={() => setActiveTab('TRADES')}
            className={`text-lg font-bold tracking-wide transition-colors ${
              activeTab === 'TRADES' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
            }`}
          >
            Trades
          </button>
        </div>

        {/* Right side: DEV and YOU */}
        <div className="flex space-x-6">
          <button
            onClick={() => setActiveTab('DEV')}
            className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
              activeTab === 'DEV' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
            }`}
          >
            <Filter size={18} className="mr-1" />
            DEV
          </button>

          <button
            onClick={() => setActiveTab('YOU')}
            className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
              activeTab === 'YOU' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
            }`}
          >
            <User size={18} className="mr-1" />
            YOU
          </button>
        </div>
      </div>

      {/* Fixed Column Headers - Transparent */}
      <div className="flex items-center px-2 py-3 text-gray-400 text-xs border-b border-gray-800 sticky top-0 z-10">
        {/* Amount / USD Toggle */}
        <div className="flex-1 flex items-center justify-center space-x-2 pl-1">
          <span>Amount</span>
          <button
            onClick={() => setShowUSD(!showUSD)}
            className={`w-5 h-5 flex items-center justify-center rounded-full border transition-transform ${
              showUSD ? 'border-green-500 text-green-400' : 'border-gray-500 text-gray-400'
            } hover:scale-110`}
            title="Toggle USD/SOL"
          >
            <DollarSign size={10} />
          </button>
        </div>

        {/* Market Cap / Price Toggle */}
        <div className="w-24 flex items-center justify-center space-x-1 px-2">
          <span>{showPrice ? 'Price' : 'MC'}</span>
          <button
            onClick={() => setShowPrice(!showPrice)}
            className="hover:text-white transition-colors"
            title="Toggle MC / Price"
          >
            <ArrowLeftRight size={14} />
          </button>
        </div>

        {/* Trader */}
        <div className="w-20 text-center px-1">Trader</div>

        {/* Age Sort */}
        <div
          className="w-20 flex items-center justify-center cursor-pointer pr-1"
          onClick={() => setSortAsc(!sortAsc)}
        >
          <span>Age</span>
          {sortAsc ? <ChevronDown size={12} className="ml-1" /> : <ChevronUp size={12} className="ml-1" />}
        </div>
      </div>

      {/* Scrollable Trade Entries - Full Height */}
      <div className="divide-y divide-gray-800 overflow-y-auto flex-1">
        {activeTab === 'TRADES' && isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-400">Loading trade data...</div>
          </div>
        )}

        {activeTab === 'TRADES' && !isLoading && getCurrentData().length === 0 && (
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-400">
              {error ? 'Failed to load trade data' : 'No trades available'}
            </div>
          </div>
        )}

        {getCurrentData().map((trade, index) => (
          <div key={trade.id || index} className="flex items-center px-2 py-2 hover:bg-gray-800/50 transition-colors">
            <div
              className={`flex-1 font-medium flex items-center justify-center space-x-1 pl-1 ${
                trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
              }`}
            >
              {!showUSD && (
                <img
                  src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                  alt="sol"
                  className="w-3 h-3"
                />
              )}
              <span className="text-center">{showUSD ? trade.usdAmount : trade.amount}</span>
            </div>
            <div className="w-24 text-center text-gray-300 font-medium px-2">{showPrice ? trade.price : trade.mc}</div>
            <div
              className="w-20 text-center text-gray-300 font-medium cursor-pointer hover:text-blue-400 transition-colors px-1 truncate"
              onClick={() => handleTraderClick(trade.txHash)}
              title={`Click to view transaction on Solscan: ${trade.txHash}`}
            >
              {trade.trader}
            </div>
            <div className="w-20 text-center text-gray-400 font-medium pr-1 truncate">{formatRealTimeAge(trade.timestamp)}</div>
          </div>
        ))}
      </div>

    </div>
  );
};

export default Trades;
