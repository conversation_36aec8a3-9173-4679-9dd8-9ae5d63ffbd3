import { useState, useMemo, useEffect } from 'react';
import { ChevronUp, ChevronDown, ExternalLink } from 'lucide-react';

import { formatSmallNumber } from '@/utils/numberFormatting';

interface TradersProps {
  view?: 'dev' | 'you';
  tradeData?: {
    trades: any[];
    isLoading: boolean;
    isConnected: boolean;
    error: string | null;
    lastUpdate: number | null;
    latestRawTrade: any;
  };
}

const Traders: React.FC<TradersProps> = ({ view, tradeData }) => {
  const [sortAsc, setSortAsc] = useState(false); // Default to newest first
  const [currentTime, setCurrentTime] = useState(Date.now());

  // Use trade data from props (passed from Tables component)
  const { trades = [], isLoading = false, isConnected = false, error = null } = tradeData || {};

  // Real-time age updates (similar to side panel implementation)
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, []);

  // Format age with real-time updates (matching side panel implementation)
  const formatRealTimeAge = (timestamp: number): string => {
    // Handle invalid timestamps - show "just now" instead of "unknown"
    if (!timestamp || isNaN(timestamp) || timestamp <= 0) {
      console.warn('⚠️ Invalid timestamp in formatRealTimeAge:', timestamp);
      return 'just now';
    }

    const diffSeconds = Math.floor((currentTime - timestamp) / 1000);

    // Handle negative or invalid time differences - show "just now" instead of "unknown"
    if (isNaN(diffSeconds) || diffSeconds < 0) {
      console.warn('⚠️ Invalid time difference:', { currentTime, timestamp, diffSeconds });
      return 'just now';
    }

    if (diffSeconds < 1) {
      return 'just now';
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffSeconds / 86400)}d ago`;
    }
  };

  // Format market cap from trade data (matching trade_example.md logic)
  const formatMarketCap = (trade: any): string => {
    let marketCap = trade.marketCap || 0;

    if (marketCap >= 1e12) {
      return (marketCap / 1e12).toFixed(2) + 'T';
    } else if (marketCap >= 1e9) {
      return (marketCap / 1e9).toFixed(2) + 'B';
    } else if (marketCap >= 1e6) {
      return (marketCap / 1e6).toFixed(2) + 'M';
    } else if (marketCap >= 1e3) {
      return (marketCap / 1e3).toFixed(2) + 'K';
    } else {
      return marketCap.toFixed(0);
    }
  };

  // Format token amount from trade data (using actualTokenAmount for actual token quantity)
  const formatTokenAmount = (trade: any): string => {
    // Use actualTokenAmount from FormattedTrade (actual token quantity being traded)
    let tokenAmount = trade.actualTokenAmount || 0;

    let formattedAmount = '';
    if (tokenAmount >= 1e12) {
      formattedAmount = (tokenAmount / 1e12).toFixed(3) + 'T';
    } else if (tokenAmount >= 1e9) {
      formattedAmount = (tokenAmount / 1e9).toFixed(3) + 'B';
    } else if (tokenAmount >= 1e6) {
      formattedAmount = (tokenAmount / 1e6).toFixed(3) + 'M';
    } else if (tokenAmount >= 1e3) {
      formattedAmount = (tokenAmount / 1e3).toFixed(3) + 'K';
    } else {
      // For amounts less than 1000, show with 3 decimal places and add thousand separators
      formattedAmount = tokenAmount.toLocaleString('en-US', {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
      });
    }

    // Return just the formatted amount without token symbol
    return formattedAmount;
  };

  // Format SOL amount from trade data (using token_amount_vs for SOL equivalent)
  const formatSOLAmount = (trade: any): string => {
    // Use tokenAmount which should contain token_amount_vs (SOL amount) from the formatted trade
    let solAmount = trade.tokenAmount || 0;

    let formattedAmount = '';
    if (solAmount >= 1e12) {
      formattedAmount = (solAmount / 1e12).toFixed(3) + 'T';
    } else if (solAmount >= 1e9) {
      formattedAmount = (solAmount / 1e9).toFixed(3) + 'B';
    } else if (solAmount >= 1e6) {
      formattedAmount = (solAmount / 1e6).toFixed(3) + 'M';
    } else if (solAmount >= 1e3) {
      formattedAmount = (solAmount / 1e3).toFixed(3) + 'K';
    } else {
      // For amounts less than 1000, show with 3 decimal places and add thousand separators
      formattedAmount = solAmount.toLocaleString('en-US', {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
      });
    }

    // Return just the formatted amount without SOL text
    return formattedAmount;
  };

  // Format trade USD value (keeping for reference, but will be replaced with SOL)
  const formatTradeValue = (trade: any): string => {
    let usdValue = trade.tokenAmountUsd || 0;
    return formatSmallNumber(usdValue);
  };

  // Format transaction hash (return just the shortened hash)
  const formatHash = (trade: any): string => {
    let hash = trade.txHash || '';

    if (!hash || hash === 'N/A') {
      return 'N/A';
    }

    if (hash.length > 8) {
      return `${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;
    }

    return hash;
  };

  // Get full hash for click handler
  const getFullHash = (trade: any): string => {
    return trade.txHash || '';
  };

  // Handle hash click to open Solscan
  const handleHashClick = (hash: string) => {
    if (hash && hash !== 'N/A') {
      const explorerUrl = `https://solscan.io/tx/${hash}`;
      window.open(explorerUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const getCurrentData = () => {
    let data = trades;

    // Debug logging to see what data we have
    console.log('🔍 Bottom table debug:', {
      totalTrades: trades.length,
      view: view,
      sampleTrade: trades[0],
      traderValues: trades.slice(0, 3).map(t => t.trader)
    });

    // TODO: Implement proper dev wallet and user wallet identification
    // For now, show all trades since we don't have wallet identification logic
    if (view === 'dev') {
      // Filter for dev wallets - would need real dev wallet addresses
      // For now, show all trades as placeholder
      data = trades;
    } else if (view === 'you') {
      // Filter for user's wallet - would need user wallet identification
      // For now, show all trades as placeholder
      data = trades;
    }

    const sortedData = [...data].sort((a, b) => {
      return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
    });

    console.log('📊 Bottom table final data:', {
      filteredCount: sortedData.length,
      firstTrade: sortedData[0]
    });

    return sortedData;
  };

  // ---------- UI ----------
  return (
    <div className="rounded-lg overflow-hidden">
      {/* Connection Status */}
      <div className="flex items-center justify-between px-6 py-2 bg-neutral-900/40 border-b border-neutral-700">
        <div className="flex items-center space-x-2 text-xs">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
          <span className="text-neutral-400">
            {isLoading ? 'Loading trades...' : isConnected ? `Live • ${trades.length} trades` : 'Disconnected'}
          </span>
          {error && <span className="text-red-400">Error: {error}</span>}
        </div>
        <div className="text-xs text-neutral-500">
          {getCurrentData().length > 0 ? `${getCurrentData().length} trades` : 'No trades available'}
        </div>
      </div>

      {/* Table Structure with Fixed Height and Scroll */}
      <div className="flex flex-col h-[600px]"> {/* Increased height container */}
        {/* Fixed Header */}
        <div className="flex-shrink-0 border-b border-neutral-700 bg-[#141416]">
          <div className="flex text-neutral-400 text-xs">
            <div className="flex-1 px-4 py-3">
              <div
                className="flex items-center cursor-pointer hover:text-white transition-colors"
                onClick={() => setSortAsc(!sortAsc)}
              >
                <span>Age</span>
                {sortAsc ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
              </div>
            </div>
            <div className="flex-1 px-4 py-3">Type</div>
            <div className="flex-1 px-4 py-3">Market Cap</div>
            <div className="flex-1 px-4 py-3">Amount</div>
            <div className="flex-1 px-4 py-3">Total SOL</div>
            <div className="flex-1 px-4 py-3">Hash</div>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="divide-y divide-neutral-800">
            {isLoading && (
              <div className="text-center py-8">
                <div className="text-neutral-400">Loading trade data...</div>
              </div>
            )}

            {!isLoading && getCurrentData().length === 0 && (
              <div className="text-center py-8">
                <div className="text-neutral-400">
                  {error ? 'Failed to load trade data' : 'No trades available'}
                </div>
                <div className="text-xs text-red-400 mt-2">
                  Debug: Total trades: {trades.length}, View: {view}, Filtered: {getCurrentData().length}
                </div>
              </div>
            )}

            {/* Debug row - temporary */}
            {!isLoading && trades.length > 0 && getCurrentData().length === 0 && (
              <div className="bg-red-900/20 text-center py-4">
                <div className="text-red-400 text-sm">
                  🐛 DEBUG: Have {trades.length} trades but filter returned 0. View: "{view}"
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Sample trader values: {trades.slice(0, 3).map(t => t.trader).join(', ')}
                </div>
              </div>
            )}

            {getCurrentData().map((trade, index) => {
              console.log(`🔄 Rendering trade ${index}:`, trade);
              return (
                <div key={trade.id || index} className="flex hover:bg-neutral-800/50 transition-colors border-b border-neutral-800 last:border-b-0">
                  {/* Age */}
                  <div className="flex-1 px-4 py-2 text-neutral-300 font-mono text-sm">
                    {formatRealTimeAge(trade.timestamp)}
                  </div>

                  {/* Type */}
                  <div className="flex-1 px-4 py-2">
                    <span className={`font-medium ${
                      trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {trade.type || 'N/A'}
                    </span>
                  </div>

                  {/* Market Cap */}
                  <div className="flex-1 px-4 py-2 text-neutral-300 font-mono">
                    ${formatMarketCap(trade)}
                  </div>

                  {/* Amount */}
                  <div className="flex-1 px-4 py-2 text-neutral-300 font-mono">
                    {formatTokenAmount(trade)}
                  </div>

                  {/* Total SOL */}
                  <div className="flex-1 px-4 py-2 font-mono">
                    <div className={`flex items-center space-x-1 ${
                      trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      <img
                        src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                        alt="SOL"
                        className="w-4 h-4"
                      />
                      <span>{formatSOLAmount(trade)}</span>
                    </div>
                  </div>

                  {/* Hash */}
                  <div className="flex-1 px-4 py-2 text-blue-400">
                    <div
                      className="flex items-center space-x-1 cursor-pointer hover:text-blue-300 transition-colors font-mono text-sm"
                      onClick={() => handleHashClick(getFullHash(trade))}
                      title={`Click to view transaction on Solscan: ${getFullHash(trade)}`}
                    >
                      <span>{formatHash(trade)}</span>
                      <ExternalLink size={12} />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

    </div>
  );
};

export default Traders;
